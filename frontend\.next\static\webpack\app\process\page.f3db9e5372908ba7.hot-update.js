"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./components/chat-interface.tsx":
/*!***************************************!*\
  !*** ./components/chat-interface.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Learning suggestions\nconst learningSuggestions = [\n    \"Explain the main concepts in this document\",\n    \"Create a summary of the key points\",\n    \"Generate practice questions about this content\",\n    \"How does this relate to [topic]?\",\n    \"What are the practical applications of this?\"\n];\nfunction ChatInterface() {\n    let { state, setState, documentId } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(state || []);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"openai\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [suggestionsOpen, setSuggestionsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Update external state when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (setState) {\n                setState(messages);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        setState\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = async ()=>{\n        if (!input.trim()) return;\n        // Add user message\n        const userMessage = {\n            id: Date.now().toString(),\n            content: input,\n            role: \"user\",\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentInput = input;\n        setInput(\"\");\n        setIsLoading(true);\n        try {\n            // Call the real chat API\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.chatApi.sendMessage(currentInput, documentId === null || documentId === void 0 ? void 0 : documentId.toString(), selectedModel);\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                content: response.message || response.response || \"I received your message but couldn't generate a response.\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            var _error_response, _error_response1, _error_response_data, _error_response2;\n            console.error(\"Error sending message:\", error);\n            let errorContent = \"Sorry, I'm having trouble connecting right now. Please try again later.\";\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                errorContent = \"Please log in to continue chatting.\";\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 429) {\n                errorContent = \"You've reached your chat limit. Please try again later.\";\n            } else if ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) {\n                errorContent = error.response.data.error;\n            }\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: errorContent,\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleAttachmentClick = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    const handleImageClick = ()=>{\n        var _imageInputRef_current;\n        (_imageInputRef_current = imageInputRef.current) === null || _imageInputRef_current === void 0 ? void 0 : _imageInputRef_current.click();\n    };\n    const handleFileChange = (e)=>{\n        if (e.target.files && e.target.files.length > 0) {\n            const file = e.target.files[0];\n            // Here you would typically upload the file and then reference it in a message\n            const userMessage = {\n                id: Date.now().toString(),\n                content: \"Attached file: \".concat(file.name),\n                role: \"user\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // Reset the file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n            // Simulate AI response\n            setIsLoading(true);\n            setTimeout(()=>{\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    content: \"I've received your file: \".concat(file.name, \". What would you like to know about it?\"),\n                    role: \"assistant\",\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]);\n                setIsLoading(false);\n            }, 1000);\n        }\n    };\n    const handleSuggestionClick = (suggestion)=>{\n        setInput(suggestion);\n        setSuggestionsOpen(false);\n    };\n    const isEmpty = messages.length === 0 && !isLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-black\",\n        ref: chatContainerRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-medium\",\n                                children: \"Chat with the AI Tutor\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                value: selectedModel,\n                                onValueChange: setSelectedModel,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                        className: \"w-32 bg-neutral-800 border-neutral-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"openai\",\n                                                children: \"OpenAI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"gemini\",\n                                                children: \"Gemini\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    isEmpty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-sm text-muted-foreground\",\n                        children: \"Ask anything or use the suggestions below\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    documentId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-xs text-purple-400\",\n                        children: [\n                            \"Connected to document #\",\n                            documentId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: isEmpty ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex flex-col items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 rounded-full bg-neutral-800 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-12 w-12 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 p-4\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 max-w-[80%] \".concat(message.role === \"user\" ? \"flex-row-reverse\" : \"flex-row\"),\n                                    children: [\n                                        message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                            className: \"bg-purple-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-5 w-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                                    alt: \"Cognimosity Logo\",\n                                                    fill: true,\n                                                    className: \"object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                            className: \"bg-neutral-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg p-3 \".concat(message.role === \"user\" ? \"bg-purple-600 text-white\" : \"bg-neutral-800 text-neutral-100\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                        className: \"bg-purple-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-5 w-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                                alt: \"Cognimosity Logo\",\n                                                fill: true,\n                                                className: \"object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-lg p-3 bg-neutral-800 text-neutral-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.3s]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.15s]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-neutral-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__.Collapsible, {\n                            open: suggestionsOpen,\n                            onOpenChange: setSuggestionsOpen,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__.CollapsibleTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Learning Suggestions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 transition-transform \".concat(suggestionsOpen ? \"rotate-180\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__.CollapsibleContent, {\n                                    className: \"space-y-2 pt-2\",\n                                    children: learningSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-sm\",\n                                            onClick: ()=>handleSuggestionClick(suggestion),\n                                            children: suggestion\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"file\",\n                                    ref: fileInputRef,\n                                    className: \"hidden\",\n                                    onChange: handleFileChange,\n                                    accept: \".pdf,.doc,.docx,.txt\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"file\",\n                                    ref: imageInputRef,\n                                    className: \"hidden\",\n                                    onChange: handleFileChange,\n                                    accept: \"image/*\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleAttachmentClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleImageClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyDown: handleKeyDown,\n                                    placeholder: \"Type your message...\",\n                                    className: \"min-h-[60px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleSendMessage,\n                                    disabled: !input.trim() || isLoading,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"iRGZukUk/y8ndrTgHaNiunYon9U=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat-interface.tsx\n"));

/***/ })

});