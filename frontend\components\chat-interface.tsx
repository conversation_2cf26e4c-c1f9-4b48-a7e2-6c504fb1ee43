"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Send, User, Paperclip, ImageIcon, ChevronDown, MessageSquare } from "lucide-react"
import { Avatar } from "@/components/ui/avatar"
import NextImage from "next/image"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { chatApi } from "@/lib/api"

type Message = {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
}

interface ChatInterfaceProps {
  state?: Message[]
  setState?: React.Dispatch<React.SetStateAction<Message[]>>
  documentId?: string | number
  selectedModel?: string
  onModelChange?: (model: string) => void
}

// Learning suggestions
const learningSuggestions = [
  "Explain the main concepts in this document",
  "Create a summary of the key points",
  "Generate practice questions about this content",
  "How does this relate to [topic]?",
  "What are the practical applications of this?",
]

export function ChatInterface({
  state,
  setState,
  documentId,
  selectedModel: propSelectedModel = "openai",
  onModelChange
}: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>(state || [])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [selectedModel, setSelectedModel] = useState(propSelectedModel)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const imageInputRef = useRef<HTMLInputElement>(null)
  const [suggestionsOpen, setSuggestionsOpen] = useState(false)
  const chatContainerRef = useRef<HTMLDivElement>(null)

  // Sync with external selectedModel prop changes
  useEffect(() => {
    setSelectedModel(propSelectedModel)
  }, [propSelectedModel])

  // Handle model change
  const handleModelChange = (model: string) => {
    setSelectedModel(model)
    if (onModelChange) {
      onModelChange(model)
    }
  }

  // Update external state when messages change
  useEffect(() => {
    if (setState) {
      setState(messages)
    }
  }, [messages, setState])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  const handleSendMessage = async () => {
    if (!input.trim()) return

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      role: "user",
      timestamp: new Date(),
    }
    setMessages((prev) => [...prev, userMessage])
    const currentInput = input
    setInput("")
    setIsLoading(true)

    try {
      // Call the real API
      const response = await chatApi.sendMessage(
        currentInput,
        documentId?.toString(),
        selectedModel
      )

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.message || response.response || "I received your message but couldn't generate a response.",
        role: "assistant",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, assistantMessage])
    } catch (error) {
      console.error("Error sending message:", error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "I'm sorry, I'm having trouble responding right now. Please try again.",
        role: "assistant",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleAttachmentClick = () => {
    fileInputRef.current?.click()
  }

  const handleImageClick = () => {
    imageInputRef.current?.click()
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]
      // For now, just show a message about file upload
      // In a full implementation, you would upload the file first
      const userMessage: Message = {
        id: Date.now().toString(),
        content: `I'd like to upload and discuss this file: ${file.name}`,
        role: "user",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, userMessage])

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }

      // Send the message about the file to the AI
      setIsLoading(true)
      chatApi.sendMessage(`I'd like to upload and discuss this file: ${file.name}`, documentId?.toString(), selectedModel)
        .then(response => {
          const assistantMessage: Message = {
            id: (Date.now() + 1).toString(),
            content: response.message || response.response || "I understand you'd like to discuss a file. Please describe what you'd like to know about it.",
            role: "assistant",
            timestamp: new Date(),
          }
          setMessages((prev) => [...prev, assistantMessage])
        })
        .catch(error => {
          console.error("Error sending file message:", error)
          const errorMessage: Message = {
            id: (Date.now() + 1).toString(),
            content: "I received your file reference, but I'm having trouble responding right now. Please try asking a question about it.",
            role: "assistant",
            timestamp: new Date(),
          }
          setMessages((prev) => [...prev, errorMessage])
        })
        .finally(() => {
          setIsLoading(false)
        })
    }
  }

  const handleSuggestionClick = (suggestion: string) => {
    setInput(suggestion)
    setSuggestionsOpen(false)
  }

  const isEmpty = messages.length === 0 && !isLoading

  return (
    <div className="flex flex-col h-full bg-black" ref={chatContainerRef}>
      <div className="p-4 border-b border-neutral-800">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-xl font-medium">Chat with the AI Tutor</h2>
          <Select value={selectedModel} onValueChange={handleModelChange}>
            <SelectTrigger className="w-32 bg-neutral-800 border-neutral-700">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="openai">OpenAI</SelectItem>
              <SelectItem value="gemini">Gemini</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {isEmpty && (
          <p className="text-center text-sm text-muted-foreground">Ask anything or use the suggestions below</p>
        )}
        {documentId && (
          <p className="text-center text-xs text-purple-400">Connected to document #{documentId}</p>
        )}
      </div>

      <div className="flex-1 overflow-auto">
        {isEmpty ? (
          <div className="h-full flex flex-col items-center justify-center">
            <div className="p-6 rounded-full bg-neutral-800 mb-4">
              <MessageSquare className="h-12 w-12 text-muted-foreground" />
            </div>
          </div>
        ) : (
          <div className="space-y-4 p-4">
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
                <div className={`flex gap-3 max-w-[80%] ${message.role === "user" ? "flex-row-reverse" : "flex-row"}`}>
                  {message.role === "assistant" ? (
                    <Avatar className="bg-purple-600">
                      <div className="relative h-5 w-5">
                        <NextImage
                          src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png"
                          alt="Cognimosity Logo"
                          fill
                          className="object-contain"
                        />
                      </div>
                    </Avatar>
                  ) : (
                    <Avatar className="bg-neutral-700">
                      <User className="h-5 w-5" />
                    </Avatar>
                  )}
                  <div
                    className={`rounded-lg p-3 ${
                      message.role === "user" ? "bg-purple-600 text-white" : "bg-neutral-800 text-neutral-100"
                    }`}
                  >
                    <p className="text-sm">{message.content}</p>
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="flex gap-3">
                  <Avatar className="bg-purple-600">
                    <div className="relative h-5 w-5">
                      <NextImage
                        src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png"
                        alt="Cognimosity Logo"
                        fill
                        className="object-contain"
                      />
                    </div>
                  </Avatar>
                  <div className="rounded-lg p-3 bg-neutral-800 text-neutral-100">
                    <div className="flex space-x-2">
                      <div className="h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.3s]"></div>
                      <div className="h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.15s]"></div>
                      <div className="h-2 w-2 rounded-full bg-neutral-400 animate-bounce"></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      <div className="p-4 border-t border-neutral-800">
        <div className="flex flex-col gap-4">
          <Collapsible open={suggestionsOpen} onOpenChange={setSuggestionsOpen}>
            <CollapsibleTrigger asChild>
              <Button variant="outline" className="w-full justify-between">
                <span>Learning Suggestions</span>
                <ChevronDown className={`h-4 w-4 transition-transform ${suggestionsOpen ? "rotate-180" : ""}`} />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-2 pt-2">
              {learningSuggestions.map((suggestion, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  className="w-full justify-start text-sm"
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  {suggestion}
                </Button>
              ))}
            </CollapsibleContent>
          </Collapsible>

          <div className="flex gap-2">
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              onChange={handleFileChange}
              accept=".pdf,.doc,.docx,.txt"
            />
            <input type="file" ref={imageInputRef} className="hidden" onChange={handleFileChange} accept="image/*" />
            <Button variant="outline" size="icon" onClick={handleAttachmentClick}>
              <Paperclip className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={handleImageClick}>
              <ImageIcon className="h-4 w-4" />
            </Button>
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message..."
              className="min-h-[60px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500"
            />
            <Button onClick={handleSendMessage} disabled={!input.trim() || isLoading}>
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
