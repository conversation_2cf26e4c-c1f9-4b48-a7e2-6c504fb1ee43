"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./components/chat-interface.tsx":
/*!***************************************!*\
  !*** ./components/chat-interface.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Learning suggestions\nconst learningSuggestions = [\n    \"Explain the main concepts in this document\",\n    \"Create a summary of the key points\",\n    \"Generate practice questions about this content\",\n    \"How does this relate to [topic]?\",\n    \"What are the practical applications of this?\"\n];\nfunction ChatInterface() {\n    let { state, setState, documentId } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(state || []);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [suggestionsOpen, setSuggestionsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Update external state when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (setState) {\n                setState(messages);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        setState\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = async ()=>{\n        if (!input.trim()) return;\n        // Add user message\n        const userMessage = {\n            id: Date.now().toString(),\n            content: input,\n            role: \"user\",\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentInput = input;\n        setInput(\"\");\n        setIsLoading(true);\n        try {\n            // Call the real chat API\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.chatApi.sendMessage(currentInput, documentId === null || documentId === void 0 ? void 0 : documentId.toString());\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                content: response.message || response.response || \"I received your message but couldn't generate a response.\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"Sorry, I'm having trouble connecting right now. Please try again later.\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleAttachmentClick = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    const handleImageClick = ()=>{\n        var _imageInputRef_current;\n        (_imageInputRef_current = imageInputRef.current) === null || _imageInputRef_current === void 0 ? void 0 : _imageInputRef_current.click();\n    };\n    const handleFileChange = (e)=>{\n        if (e.target.files && e.target.files.length > 0) {\n            const file = e.target.files[0];\n            // Here you would typically upload the file and then reference it in a message\n            const userMessage = {\n                id: Date.now().toString(),\n                content: \"Attached file: \".concat(file.name),\n                role: \"user\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // Reset the file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n            // Simulate AI response\n            setIsLoading(true);\n            setTimeout(()=>{\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    content: \"I've received your file: \".concat(file.name, \". What would you like to know about it?\"),\n                    role: \"assistant\",\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]);\n                setIsLoading(false);\n            }, 1000);\n        }\n    };\n    const handleSuggestionClick = (suggestion)=>{\n        setInput(suggestion);\n        setSuggestionsOpen(false);\n    };\n    const isEmpty = messages.length === 0 && !isLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-black\",\n        ref: chatContainerRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-medium text-center\",\n                        children: \"Chat with the AI Tutor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    isEmpty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-sm text-muted-foreground mt-1\",\n                        children: \"Ask anything or use the suggestions below\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: isEmpty ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex flex-col items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 rounded-full bg-neutral-800 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-12 w-12 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 p-4\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 max-w-[80%] \".concat(message.role === \"user\" ? \"flex-row-reverse\" : \"flex-row\"),\n                                    children: [\n                                        message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                            className: \"bg-purple-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-5 w-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                                    alt: \"Cognimosity Logo\",\n                                                    fill: true,\n                                                    className: \"object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                            className: \"bg-neutral-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg p-3 \".concat(message.role === \"user\" ? \"bg-purple-600 text-white\" : \"bg-neutral-800 text-neutral-100\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                        className: \"bg-purple-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-5 w-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                                alt: \"Cognimosity Logo\",\n                                                fill: true,\n                                                className: \"object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-lg p-3 bg-neutral-800 text-neutral-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.3s]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.15s]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-neutral-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__.Collapsible, {\n                            open: suggestionsOpen,\n                            onOpenChange: setSuggestionsOpen,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__.CollapsibleTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Learning Suggestions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 transition-transform \".concat(suggestionsOpen ? \"rotate-180\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__.CollapsibleContent, {\n                                    className: \"space-y-2 pt-2\",\n                                    children: learningSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-sm\",\n                                            onClick: ()=>handleSuggestionClick(suggestion),\n                                            children: suggestion\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"file\",\n                                    ref: fileInputRef,\n                                    className: \"hidden\",\n                                    onChange: handleFileChange,\n                                    accept: \".pdf,.doc,.docx,.txt\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"file\",\n                                    ref: imageInputRef,\n                                    className: \"hidden\",\n                                    onChange: handleFileChange,\n                                    accept: \"image/*\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleAttachmentClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleImageClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyDown: handleKeyDown,\n                                    placeholder: \"Type your message...\",\n                                    className: \"min-h-[60px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleSendMessage,\n                                    disabled: !input.trim() || isLoading,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"oz141vkl/782AQ75VyiO0ICa+SE=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat-interface.tsx\n"));

/***/ })

});