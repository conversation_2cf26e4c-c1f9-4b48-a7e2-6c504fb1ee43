"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./components/chat-interface.tsx":
/*!***************************************!*\
  !*** ./components/chat-interface.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Learning suggestions\nconst learningSuggestions = [\n    \"Explain the main concepts in this document\",\n    \"Create a summary of the key points\",\n    \"Generate practice questions about this content\",\n    \"How does this relate to [topic]?\",\n    \"What are the practical applications of this?\"\n];\nfunction ChatInterface() {\n    let { state, setState, documentId } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(state || []);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"openai\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [suggestionsOpen, setSuggestionsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Update external state when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (setState) {\n                setState(messages);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        setState\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = async ()=>{\n        if (!input.trim()) return;\n        // Add user message\n        const userMessage = {\n            id: Date.now().toString(),\n            content: input,\n            role: \"user\",\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentInput = input;\n        setInput(\"\");\n        setIsLoading(true);\n        try {\n            // Call the real chat API\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.chatApi.sendMessage(currentInput, documentId === null || documentId === void 0 ? void 0 : documentId.toString(), selectedModel);\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                content: response.message || response.response || \"I received your message but couldn't generate a response.\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"Sorry, I'm having trouble connecting right now. Please try again later.\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleAttachmentClick = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    const handleImageClick = ()=>{\n        var _imageInputRef_current;\n        (_imageInputRef_current = imageInputRef.current) === null || _imageInputRef_current === void 0 ? void 0 : _imageInputRef_current.click();\n    };\n    const handleFileChange = (e)=>{\n        if (e.target.files && e.target.files.length > 0) {\n            const file = e.target.files[0];\n            // Here you would typically upload the file and then reference it in a message\n            const userMessage = {\n                id: Date.now().toString(),\n                content: \"Attached file: \".concat(file.name),\n                role: \"user\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // Reset the file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n            // Simulate AI response\n            setIsLoading(true);\n            setTimeout(()=>{\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    content: \"I've received your file: \".concat(file.name, \". What would you like to know about it?\"),\n                    role: \"assistant\",\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]);\n                setIsLoading(false);\n            }, 1000);\n        }\n    };\n    const handleSuggestionClick = (suggestion)=>{\n        setInput(suggestion);\n        setSuggestionsOpen(false);\n    };\n    const isEmpty = messages.length === 0 && !isLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-black\",\n        ref: chatContainerRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-medium text-center\",\n                        children: \"Chat with the AI Tutor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    isEmpty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-sm text-muted-foreground mt-1\",\n                        children: \"Ask anything or use the suggestions below\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: isEmpty ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex flex-col items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 rounded-full bg-neutral-800 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-12 w-12 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 p-4\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 max-w-[80%] \".concat(message.role === \"user\" ? \"flex-row-reverse\" : \"flex-row\"),\n                                    children: [\n                                        message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                            className: \"bg-purple-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-5 w-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                                    alt: \"Cognimosity Logo\",\n                                                    fill: true,\n                                                    className: \"object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                            className: \"bg-neutral-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg p-3 \".concat(message.role === \"user\" ? \"bg-purple-600 text-white\" : \"bg-neutral-800 text-neutral-100\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                        className: \"bg-purple-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-5 w-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                                alt: \"Cognimosity Logo\",\n                                                fill: true,\n                                                className: \"object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-lg p-3 bg-neutral-800 text-neutral-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.3s]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.15s]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-neutral-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__.Collapsible, {\n                            open: suggestionsOpen,\n                            onOpenChange: setSuggestionsOpen,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__.CollapsibleTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Learning Suggestions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 transition-transform \".concat(suggestionsOpen ? \"rotate-180\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__.CollapsibleContent, {\n                                    className: \"space-y-2 pt-2\",\n                                    children: learningSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-sm\",\n                                            onClick: ()=>handleSuggestionClick(suggestion),\n                                            children: suggestion\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"file\",\n                                    ref: fileInputRef,\n                                    className: \"hidden\",\n                                    onChange: handleFileChange,\n                                    accept: \".pdf,.doc,.docx,.txt\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"file\",\n                                    ref: imageInputRef,\n                                    className: \"hidden\",\n                                    onChange: handleFileChange,\n                                    accept: \"image/*\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleAttachmentClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleImageClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyDown: handleKeyDown,\n                                    placeholder: \"Type your message...\",\n                                    className: \"min-h-[60px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleSendMessage,\n                                    disabled: !input.trim() || isLoading,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"iRGZukUk/y8ndrTgHaNiunYon9U=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat-interface.tsx\n"));

/***/ })

});