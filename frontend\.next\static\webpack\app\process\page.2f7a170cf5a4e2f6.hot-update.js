"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./app/process/page.tsx":
/*!******************************!*\
  !*** ./app/process/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProcessPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/resizable */ \"(app-pages-browser)/./components/ui/resizable.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,HelpCircle,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,HelpCircle,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,HelpCircle,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,HelpCircle,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flashlight.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,HelpCircle,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,HelpCircle,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,HelpCircle,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column-increasing.js\");\n/* harmony import */ var _components_upload_content__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/upload-content */ \"(app-pages-browser)/./components/upload-content.tsx\");\n/* harmony import */ var _components_paste_content__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/paste-content */ \"(app-pages-browser)/./components/paste-content.tsx\");\n/* harmony import */ var _components_record_content__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/record-content */ \"(app-pages-browser)/./components/record-content.tsx\");\n/* harmony import */ var _components_chat_interface__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/chat-interface */ \"(app-pages-browser)/./components/chat-interface.tsx\");\n/* harmony import */ var _components_quiz_interface__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/quiz-interface */ \"(app-pages-browser)/./components/quiz-interface.tsx\");\n/* harmony import */ var _components_performance_dashboard__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/performance-dashboard */ \"(app-pages-browser)/./components/performance-dashboard.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_layout_with_sidebar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/layout-with-sidebar */ \"(app-pages-browser)/./components/layout-with-sidebar.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_blueprint_interface__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/blueprint-interface */ \"(app-pages-browser)/./components/blueprint-interface.tsx\");\n/* harmony import */ var _components_flowchart_interface__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/flowchart-interface */ \"(app-pages-browser)/./components/flowchart-interface.tsx\");\n/* harmony import */ var _components_flashcards_interface__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/flashcards-interface */ \"(app-pages-browser)/./components/flashcards-interface.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProcessPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const type = searchParams.get(\"type\") || \"upload\";\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chat\");\n    const [showFullScreen, setShowFullScreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [uploadedData, setUploadedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { theme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_12__.useTheme)();\n    const tabsListRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // State preservation\n    const [chatState, setChatState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [summaryState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [chapterState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProcessPage.useEffect\": ()=>{\n            document.body.classList.add(\"page-transition\");\n            const uploadedFilesStr = localStorage.getItem(\"uploadedFiles\");\n            const filePreviewUrlsStr = localStorage.getItem(\"filePreviewUrls\");\n            if (uploadedFilesStr && type === \"upload\") {\n                try {\n                    const uploadedFiles = JSON.parse(uploadedFilesStr);\n                    if (uploadedFiles.length > 0) {\n                        var _file_name_split_pop;\n                        const file = uploadedFiles[0];\n                        let previewUrl = null;\n                        if (filePreviewUrlsStr) {\n                            const urls = JSON.parse(filePreviewUrlsStr);\n                            if (urls.length > 0) {\n                                previewUrl = urls[0];\n                            }\n                        }\n                        setUploadedData({\n                            type: \"file\",\n                            name: file.name,\n                            size: file.size,\n                            previewUrl: previewUrl,\n                            fileType: file.type || ((_file_name_split_pop = file.name.split('.').pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase())\n                        });\n                        setShowFullScreen(false);\n                    }\n                } catch (error) {\n                    console.error(\"Error parsing uploaded files:\", error);\n                }\n            } else if (type === \"paste\") {\n                const pastedContentStr = localStorage.getItem(\"pastedContent\");\n                if (pastedContentStr) {\n                    try {\n                        const pastedContent = JSON.parse(pastedContentStr);\n                        setUploadedData({\n                            type: \"text\",\n                            content: pastedContent.content || pastedContent.url || \"Pasted content\"\n                        });\n                        setShowFullScreen(false);\n                    } catch (error) {\n                        console.error(\"Error parsing pasted content:\", error);\n                    }\n                }\n            } else if (type === \"record\") {\n                const recordedAudioStr = localStorage.getItem(\"recordedAudio\");\n                if (recordedAudioStr) {\n                    try {\n                        const recordedAudio = JSON.parse(recordedAudioStr);\n                        setUploadedData({\n                            type: \"audio\",\n                            name: \"Recording-\" + new Date().toISOString().split(\"T\")[0] + \".wav\",\n                            duration: recordedAudio.duration || \"00:00\"\n                        });\n                        setShowFullScreen(false);\n                    } catch (error) {\n                        console.error(\"Error parsing recorded audio:\", error);\n                    }\n                }\n            }\n            return ({\n                \"ProcessPage.useEffect\": ()=>{\n                    document.body.classList.remove(\"page-transition\");\n                }\n            })[\"ProcessPage.useEffect\"];\n        }\n    }[\"ProcessPage.useEffect\"], [\n        type\n    ]);\n    const renderInputComponent = ()=>{\n        if (uploadedData) {\n            if (uploadedData.type === \"file\") {\n                var _uploadedData_fileType, _uploadedData_fileType1;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: uploadedData.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-lg flex flex-col items-center justify-center \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-800\"),\n                                children: uploadedData.fileType === 'pdf' || uploadedData.fileType === 'application/pdf' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                    src: uploadedData.previewUrl,\n                                    title: uploadedData.name,\n                                    width: \"100%\",\n                                    height: \"600px\",\n                                    style: {\n                                        border: 'none',\n                                        borderRadius: '12px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 19\n                                }, this) : ((_uploadedData_fileType = uploadedData.fileType) === null || _uploadedData_fileType === void 0 ? void 0 : _uploadedData_fileType.match(/^(jpg|jpeg|png|gif|image\\/jpeg|image\\/png|image\\/gif)$/)) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: uploadedData.previewUrl,\n                                    alt: uploadedData.name,\n                                    className: \"max-w-full object-contain\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg\",\n                                            children: uploadedData.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-500\"),\n                                            children: uploadedData.size\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-2 \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-500\"),\n                                            children: [\n                                                \"File type: \",\n                                                ((_uploadedData_fileType1 = uploadedData.fileType) === null || _uploadedData_fileType1 === void 0 ? void 0 : _uploadedData_fileType1.toUpperCase()) || 'Unknown'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, this);\n            } else if (uploadedData.type === \"text\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Pasted Content\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-lg p-4 \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-800\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: uploadedData.content\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, this);\n            } else if (uploadedData.type === \"audio\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: uploadedData.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-lg p-4 flex flex-col items-center \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-800\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-24 rounded-md mb-4 \".concat(theme === \"light\" ? \"bg-gray-200\" : \"bg-neutral-700\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                    controls: true,\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                        src: \"#\",\n                                        type: \"audio/wav\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-2 \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-500\"),\n                                    children: [\n                                        \"Duration: \",\n                                        uploadedData.duration\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n        switch(type){\n            case \"upload\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_upload_content__WEBPACK_IMPORTED_MODULE_5__.UploadContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 29\n                }, this);\n            case \"paste\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_paste_content__WEBPACK_IMPORTED_MODULE_6__.PasteContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 28\n                }, this);\n            case \"record\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_record_content__WEBPACK_IMPORTED_MODULE_7__.RecordContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 29\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_upload_content__WEBPACK_IMPORTED_MODULE_5__.UploadContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const renderOutputComponent = ()=>{\n        const documentId = searchParams.get(\"documentId\") ? parseInt(searchParams.get(\"documentId\")) : undefined;\n        switch(activeTab){\n            case \"chat\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_8__.ChatInterface, {\n                    state: chatState,\n                    setState: setChatState,\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 16\n                }, this);\n            case \"summary\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: summaryState || \"Summary content will appear here\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 16\n                }, this);\n            case \"flowchart\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flowchart_interface__WEBPACK_IMPORTED_MODULE_14__.FlowchartInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 16\n                }, this);\n            case \"flashcards\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flashcards_interface__WEBPACK_IMPORTED_MODULE_15__.FlashcardsInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 16\n                }, this);\n            case \"quizzes\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_quiz_interface__WEBPACK_IMPORTED_MODULE_9__.QuizInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 16\n                }, this);\n            case \"chapters\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: chapterState || \"Chapters content will appear here\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 16\n                }, this);\n            case \"transcript\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_dashboard__WEBPACK_IMPORTED_MODULE_10__.PerformanceDashboard, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 16\n                }, this);\n            case \"blueprint\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blueprint_interface__WEBPACK_IMPORTED_MODULE_13__.BlueprintInterface, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_8__.ChatInterface, {\n                    state: chatState,\n                    setState: setChatState,\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_with_sidebar__WEBPACK_IMPORTED_MODULE_11__.LayoutWithSidebar, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_16__.motion.div, {\n            className: \"h-[calc(100vh-65px)] overflow-hidden bg-background text-foreground flex flex-col\",\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            transition: {\n                duration: 0.3\n            },\n            children: showFullScreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_16__.motion.div, {\n                className: \"flex-1 p-4\",\n                children: renderInputComponent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__.ResizablePanelGroup, {\n                direction: \"horizontal\",\n                className: \"flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__.ResizablePanel, {\n                        defaultSize: 40,\n                        minSize: 30,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-auto\",\n                            children: renderInputComponent()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__.ResizableHandle, {\n                        withHandle: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__.ResizablePanel, {\n                        defaultSize: 60,\n                        minSize: 30,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                defaultValue: \"chat\",\n                                onValueChange: setActiveTab,\n                                value: activeTab,\n                                className: \"w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-b border-border sticky top-0 bg-background z-10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-4 overflow-x-auto scrollbar-hide\",\n                                            ref: tabsListRef,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                                className: \"h-12 w-max\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                        value: \"chat\",\n                                                        className: \"gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Chat\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                        value: \"blueprint\",\n                                                        className: \"gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Blueprint\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                        value: \"summary\",\n                                                        className: \"gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Summary\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                        value: \"flowchart\",\n                                                        className: \"gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Flowchart\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                        value: \"flashcards\",\n                                                        className: \"gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Flashcards\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                        value: \"quizzes\",\n                                                        className: \"gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Quizzes\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                        value: \"chapters\",\n                                                        className: \"gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Chapters\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                        value: \"transcript\",\n                                                        className: \"gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_HelpCircle_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Performance\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                        value: activeTab,\n                                        className: \"flex-1 p-0 m-0 overflow-hidden h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-full overflow-y-auto pb-8 scrollbar-thin scrollbar-thumb-neutral-500 scrollbar-track-transparent hover:scrollbar-thumb-neutral-400\",\n                                            children: renderOutputComponent()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                lineNumber: 222,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(ProcessPage, \"px6R/hMjop+l3tG1J8/apqqPyNk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _components_theme_provider__WEBPACK_IMPORTED_MODULE_12__.useTheme\n    ];\n});\n_c = ProcessPage;\nvar _c;\n$RefreshReg$(_c, \"ProcessPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/process/page.tsx\n"));

/***/ })

});