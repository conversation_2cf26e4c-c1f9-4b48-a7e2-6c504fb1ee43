"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./components/chat-interface.tsx":
/*!***************************************!*\
  !*** ./components/chat-interface.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Learning suggestions\nconst learningSuggestions = [\n    \"Explain the main concepts in this document\",\n    \"Create a summary of the key points\",\n    \"Generate practice questions about this content\",\n    \"How does this relate to [topic]?\",\n    \"What are the practical applications of this?\"\n];\nfunction ChatInterface(param) {\n    let { state, setState, documentId, selectedModel: propSelectedModel = \"openai\", onModelChange } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(state || []);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propSelectedModel);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [suggestionsOpen, setSuggestionsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Handle model change\n    const handleModelChange = (model)=>{\n        setSelectedModel(model);\n        if (onModelChange) {\n            onModelChange(model);\n        }\n    };\n    // Update external state when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (setState) {\n                setState(messages);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        setState\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = async ()=>{\n        if (!input.trim()) return;\n        // Add user message\n        const userMessage = {\n            id: Date.now().toString(),\n            content: input,\n            role: \"user\",\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentInput = input;\n        setInput(\"\");\n        setIsLoading(true);\n        try {\n            // Call the real API\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.chatApi.sendMessage(currentInput, documentId === null || documentId === void 0 ? void 0 : documentId.toString(), selectedModel);\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                content: response.message || response.response || \"I received your message but couldn't generate a response.\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"I'm sorry, I'm having trouble responding right now. Please try again.\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleAttachmentClick = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    const handleImageClick = ()=>{\n        var _imageInputRef_current;\n        (_imageInputRef_current = imageInputRef.current) === null || _imageInputRef_current === void 0 ? void 0 : _imageInputRef_current.click();\n    };\n    const handleFileChange = (e)=>{\n        if (e.target.files && e.target.files.length > 0) {\n            const file = e.target.files[0];\n            // For now, just show a message about file upload\n            // In a full implementation, you would upload the file first\n            const userMessage = {\n                id: Date.now().toString(),\n                content: \"I'd like to upload and discuss this file: \".concat(file.name),\n                role: \"user\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // Reset the file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n            // Send the message about the file to the AI\n            setIsLoading(true);\n            _lib_api__WEBPACK_IMPORTED_MODULE_8__.chatApi.sendMessage(\"I'd like to upload and discuss this file: \".concat(file.name), documentId === null || documentId === void 0 ? void 0 : documentId.toString(), selectedModel).then((response)=>{\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    content: response.message || response.response || \"I understand you'd like to discuss a file. Please describe what you'd like to know about it.\",\n                    role: \"assistant\",\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]);\n            }).catch((error)=>{\n                console.error(\"Error sending file message:\", error);\n                const errorMessage = {\n                    id: (Date.now() + 1).toString(),\n                    content: \"I received your file reference, but I'm having trouble responding right now. Please try asking a question about it.\",\n                    role: \"assistant\",\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }).finally(()=>{\n                setIsLoading(false);\n            });\n        }\n    };\n    const handleSuggestionClick = (suggestion)=>{\n        setInput(suggestion);\n        setSuggestionsOpen(false);\n    };\n    const isEmpty = messages.length === 0 && !isLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-black\",\n        ref: chatContainerRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-medium\",\n                                children: \"Chat with the AI Tutor\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                value: selectedModel,\n                                onValueChange: handleModelChange,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectTrigger, {\n                                        className: \"w-32 bg-neutral-800 border-neutral-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectValue, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                value: \"openai\",\n                                                children: \"OpenAI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                value: \"gemini\",\n                                                children: \"Gemini\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    isEmpty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-sm text-muted-foreground\",\n                        children: \"Ask anything or use the suggestions below\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    documentId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-xs text-purple-400\",\n                        children: [\n                            \"Connected to document #\",\n                            documentId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: isEmpty ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex flex-col items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 rounded-full bg-neutral-800 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-12 w-12 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 p-4\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 max-w-[80%] \".concat(message.role === \"user\" ? \"flex-row-reverse\" : \"flex-row\"),\n                                    children: [\n                                        message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                            className: \"bg-purple-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-5 w-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                                    alt: \"Cognimosity Logo\",\n                                                    fill: true,\n                                                    className: \"object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                            className: \"bg-neutral-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg p-3 \".concat(message.role === \"user\" ? \"bg-purple-600 text-white\" : \"bg-neutral-800 text-neutral-100\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                        className: \"bg-purple-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-5 w-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                                alt: \"Cognimosity Logo\",\n                                                fill: true,\n                                                className: \"object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-lg p-3 bg-neutral-800 text-neutral-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.3s]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.15s]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-neutral-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_7__.Collapsible, {\n                            open: suggestionsOpen,\n                            onOpenChange: setSuggestionsOpen,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_7__.CollapsibleTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Learning Suggestions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 transition-transform \".concat(suggestionsOpen ? \"rotate-180\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_7__.CollapsibleContent, {\n                                    className: \"space-y-2 pt-2\",\n                                    children: learningSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-sm\",\n                                            onClick: ()=>handleSuggestionClick(suggestion),\n                                            children: suggestion\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"file\",\n                                    ref: fileInputRef,\n                                    className: \"hidden\",\n                                    onChange: handleFileChange,\n                                    accept: \".pdf,.doc,.docx,.txt\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"file\",\n                                    ref: imageInputRef,\n                                    className: \"hidden\",\n                                    onChange: handleFileChange,\n                                    accept: \"image/*\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleAttachmentClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleImageClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyDown: handleKeyDown,\n                                    placeholder: \"Type your message...\",\n                                    className: \"min-h-[60px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleSendMessage,\n                                    disabled: !input.trim() || isLoading,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"TikzMZILocfHpA0faNa6Jyymc5g=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat-interface.tsx\n"));

/***/ })

});