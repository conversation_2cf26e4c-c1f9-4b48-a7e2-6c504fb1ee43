"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./components/flashcards-interface.tsx":
/*!*********************************************!*\
  !*** ./components/flashcards-interface.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlashcardsInterface: () => (/* binding */ FlashcardsInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_MessageSquare_Plus_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MessageSquare,Plus,RotateCcw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_MessageSquare_Plus_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MessageSquare,Plus,RotateCcw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_MessageSquare_Plus_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MessageSquare,Plus,RotateCcw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_MessageSquare_Plus_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MessageSquare,Plus,RotateCcw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_MessageSquare_Plus_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MessageSquare,Plus,RotateCcw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ FlashcardsInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction FlashcardsInterface(param) {\n    let { documentId } = param;\n    _s();\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isFlipped, setIsFlipped] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [newQuestion, setNewQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [newAnswer, setNewAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { theme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const flashcardsContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FlashcardsInterface.useEffect\": ()=>{\n            loadFlashcards();\n        }\n    }[\"FlashcardsInterface.useEffect\"], [\n        documentId\n    ]);\n    const loadFlashcards = async ()=>{\n        if (!documentId) {\n            console.warn('No document ID provided for flashcards');\n            return;\n        }\n        try {\n            setIsLoading(true);\n            // First, try to get existing flashcards\n            try {\n                const existingFlashcards = await _lib_api__WEBPACK_IMPORTED_MODULE_9__.flashcardsApi.getFlashcards(documentId);\n                if (existingFlashcards && existingFlashcards.length > 0) {\n                    const formattedFlashcards = existingFlashcards.map((card)=>({\n                            id: card.id.toString(),\n                            question: card.front,\n                            answer: card.back\n                        }));\n                    setFlashcards(formattedFlashcards);\n                    return;\n                }\n            } catch (existingError) {\n                console.log('No existing flashcards found, will generate new ones');\n            }\n            // If no existing flashcards, generate new ones\n            console.log('Generating new flashcards for document:', documentId);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_9__.flashcardsApi.generateFlashcards(documentId);\n            // After generation, fetch the newly created flashcards\n            const newFlashcards = await _lib_api__WEBPACK_IMPORTED_MODULE_9__.flashcardsApi.getFlashcards(documentId);\n            if (newFlashcards && newFlashcards.length > 0) {\n                const formattedFlashcards = newFlashcards.map((card)=>({\n                        id: card.id.toString(),\n                        question: card.front,\n                        answer: card.back\n                    }));\n                setFlashcards(formattedFlashcards);\n            } else {\n                console.warn('No flashcards were generated');\n                setFlashcards([]);\n            }\n        } catch (error) {\n            console.error('Error loading flashcards:', error);\n            // Don't set any fallback flashcards - show empty state instead\n            setFlashcards([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const currentCard = flashcards[currentIndex];\n    const handleFlip = ()=>{\n        setIsFlipped(!isFlipped);\n    };\n    const handleNext = ()=>{\n        setCurrentIndex((prev)=>(prev + 1) % flashcards.length);\n        setIsFlipped(false);\n    };\n    const handleDelete = ()=>{\n        if (flashcards.length > 1) {\n            const newFlashcards = flashcards.filter((_, index)=>index !== currentIndex);\n            setFlashcards(newFlashcards);\n            setCurrentIndex((prev)=>prev >= newFlashcards.length ? 0 : prev);\n            setIsFlipped(false);\n        }\n    };\n    const handleAddFlashcard = ()=>{\n        if (newQuestion.trim() && newAnswer.trim()) {\n            const newCard = {\n                id: Date.now().toString(),\n                question: newQuestion.trim(),\n                answer: newAnswer.trim()\n            };\n            setFlashcards([\n                ...flashcards,\n                newCard\n            ]);\n            setNewQuestion(\"\");\n            setNewAnswer(\"\");\n            setShowAddForm(false);\n        }\n    };\n    const resetToFirst = ()=>{\n        setCurrentIndex(0);\n        setIsFlipped(false);\n    };\n    const isEmpty = flashcards.length === 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: flashcardsContainerRef,\n        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"flex flex-col h-full bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"p-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"text-xl font-medium text-center\",\n                        children: \"Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    !isEmpty && !showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"text-center text-sm text-muted-foreground mt-1\",\n                        children: [\n                            \"Card \",\n                            currentIndex + 1,\n                            \" of \",\n                            flashcards.length\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                className: \"flex-1\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"h-full flex flex-col items-center justify-center p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"p-6 rounded-full bg-neutral-800 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"h-12 w-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"rounded-lg p-4 bg-purple-600/10 border border-purple-600/30 max-w-md text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"font-medium text-purple-500 mb-2\",\n                                    children: \"Loading Flashcards\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"text-sm\",\n                                    children: !documentId ? \"No document selected\" : \"Checking for existing flashcards or generating new ones...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 11\n                }, this) : isEmpty ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"h-full flex flex-col items-center justify-center p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"p-6 rounded-full bg-neutral-800 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MessageSquare_Plus_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-12 w-12 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"rounded-lg p-4 bg-purple-600/10 border border-purple-600/30 max-w-md text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"font-medium text-purple-500 mb-2\",\n                                    children: \"No Flashcards Available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"text-sm\",\n                                    children: !documentId ? \"Please select a document to view flashcards.\" : \"Unable to load or generate flashcards for this document. Please try again or create flashcards manually.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                documentId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadFlashcards,\n                                    className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"mt-3 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-md transition-colors\",\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 11\n                }, this) : showAddForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"max-w-lg mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"text-lg font-medium mb-4\",\n                                children: \"Add New Flashcard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                htmlFor: \"question\",\n                                                children: \"Question\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"question\",\n                                                placeholder: \"Enter the flashcard question\",\n                                                value: newQuestion,\n                                                onChange: (e)=>setNewQuestion(e.target.value),\n                                                className: \"min-h-[100px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                htmlFor: \"answer\",\n                                                children: \"Answer\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"answer\",\n                                                placeholder: \"Enter the flashcard answer\",\n                                                value: newAnswer,\n                                                onChange: (e)=>setNewAnswer(e.target.value),\n                                                className: \"min-h-[150px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setShowAddForm(false),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                className: \"bg-purple-600 hover:bg-purple-700\",\n                                                onClick: handleAddFlashcard,\n                                                disabled: !newQuestion.trim() || !newAnswer.trim(),\n                                                children: \"Add Flashcard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"p-6 flex flex-col items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"w-full max-w-lg mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"relative w-full perspective-1000\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"w-full relative preserve-3d\",\n                                    animate: {\n                                        rotateY: isFlipped ? 180 : 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        type: \"spring\"\n                                    },\n                                    style: {\n                                        transformStyle: \"preserve-3d\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"absolute inset-0 backface-hidden bg-neutral-800 border-neutral-700 p-6 cursor-pointer\",\n                                            onClick: handleFlip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"flex flex-col h-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"text-sm text-neutral-400 mb-2\",\n                                                        children: \"QUESTION\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"flex-1 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"text-lg\",\n                                                            children: currentCard === null || currentCard === void 0 ? void 0 : currentCard.question\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"text-xs text-neutral-500 mt-4 text-center\",\n                                                        children: \"Click to reveal answer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"absolute inset-0 backface-hidden bg-neutral-800 border-neutral-700 p-6 cursor-pointer\",\n                                            onClick: handleFlip,\n                                            style: {\n                                                transform: \"rotateY(180deg)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"flex flex-col h-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"text-sm text-neutral-400 mb-2\",\n                                                        children: \"ANSWER\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"flex-1 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"text-lg\",\n                                                            children: currentCard === null || currentCard === void 0 ? void 0 : currentCard.answer\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"text-xs text-neutral-500 mt-4 text-center\",\n                                                        children: \"Click to see question\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1417f6e2293e1f4f\" + \" \" + \"flex flex-wrap justify-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleNext,\n                                    className: \"bg-purple-600 hover:bg-purple-700\",\n                                    children: [\n                                        \"Next Card\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MessageSquare_Plus_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>setShowAddForm(true),\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MessageSquare_Plus_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Add Card\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleDelete,\n                                    variant: \"outline\",\n                                    disabled: flashcards.length <= 1,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MessageSquare_Plus_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Delete Card\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: resetToFirst,\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MessageSquare_Plus_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Reset\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"1417f6e2293e1f4f\",\n                children: \".perspective-1000{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}.preserve-3d{-webkit-transform-style:preserve-3d;-moz-transform-style:preserve-3d;transform-style:preserve-3d;min-height:300px}.backface-hidden{-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flashcards-interface.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashcardsInterface, \"dwTymN2leuv91uA/iaphbO6wvg8=\", false, function() {\n    return [\n        _components_theme_provider__WEBPACK_IMPORTED_MODULE_5__.useTheme\n    ];\n});\n_c = FlashcardsInterface;\nvar _c;\n$RefreshReg$(_c, \"FlashcardsInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/flashcards-interface.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/flowchart-interface.tsx":
/*!********************************************!*\
  !*** ./components/flowchart-interface.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlowchartInterface: () => (/* binding */ FlowchartInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Maximize,MessageSquare,RotateCcw,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Maximize,MessageSquare,RotateCcw,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Maximize,MessageSquare,RotateCcw,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Maximize,MessageSquare,RotateCcw,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Maximize,MessageSquare,RotateCcw,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Maximize,MessageSquare,RotateCcw,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var mermaid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! mermaid */ \"(app-pages-browser)/./node_modules/mermaid/dist/mermaid.core.mjs\");\n/* __next_internal_client_entry_do_not_use__ FlowchartInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction FlowchartInterface(param) {\n    let { documentId } = param;\n    _s();\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [flowchartData, setFlowchartData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const flowchartRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fullscreenFlowchartRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { theme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const flowchartContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlowchartInterface.useEffect\": ()=>{\n            // Initialize mermaid\n            mermaid__WEBPACK_IMPORTED_MODULE_7__[\"default\"].initialize({\n                startOnLoad: false,\n                theme: theme === \"dark\" ? \"dark\" : \"default\",\n                themeVariables: theme === \"dark\" ? {\n                    primaryColor: \"#6366f1\",\n                    primaryTextColor: \"#ffffff\",\n                    primaryBorderColor: \"#4f46e5\",\n                    lineColor: \"#8b5cf6\",\n                    secondaryColor: \"#7c3aed\",\n                    tertiaryColor: \"#a855f7\",\n                    background: \"#1e1b4b\",\n                    mainBkg: \"#312e81\",\n                    secondBkg: \"#3730a3\",\n                    tertiaryBkg: \"#4338ca\"\n                } : {\n                    primaryColor: \"#818cf8\",\n                    primaryTextColor: \"#ffffff\",\n                    primaryBorderColor: \"#6366f1\",\n                    lineColor: \"#6366f1\",\n                    secondaryColor: \"#c7d2fe\",\n                    tertiaryColor: \"#e0e7ff\",\n                    background: \"#ffffff\",\n                    mainBkg: \"#f3f4f6\",\n                    secondBkg: \"#e5e7eb\",\n                    tertiaryBkg: \"#f9fafb\"\n                },\n                securityLevel: 'loose',\n                fontFamily: 'inherit'\n            });\n            loadFlowchart();\n        }\n    }[\"FlowchartInterface.useEffect\"], [\n        theme,\n        documentId\n    ]);\n    const loadFlowchart = async ()=>{\n        if (!documentId) {\n            console.warn('No document ID provided for flowchart');\n            return;\n        }\n        try {\n            setIsLoading(true);\n            setError(null);\n            // First, try to get existing flowchart\n            try {\n                const existingFlowchart = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.flowchartApi.getFlowchart(documentId);\n                if (existingFlowchart && existingFlowchart.mermaid_code) {\n                    setFlowchartData(existingFlowchart.mermaid_code);\n                    await renderFlowchart(existingFlowchart.mermaid_code);\n                    return;\n                }\n            } catch (existingError) {\n                console.log('No existing flowchart found, will generate new one');\n            }\n            // If no existing flowchart, generate new one\n            console.log('Generating new flowchart for document:', documentId);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.flowchartApi.generateFlowchart(documentId);\n            const mermaidCode = response.flowchart;\n            if (mermaidCode) {\n                setFlowchartData(mermaidCode);\n                await renderFlowchart(mermaidCode);\n            } else {\n                throw new Error('No flowchart data received');\n            }\n        } catch (error) {\n            console.error(\"Failed to load flowchart:\", error);\n            setError(\"Failed to load flowchart visualization. Please try again later.\");\n            setFlowchartData(\"\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const renderFlowchart = async (mermaidCode)=>{\n        if (flowchartRef.current && mermaidCode) {\n            try {\n                // Clear the container first\n                flowchartRef.current.innerHTML = '';\n                // Validate that the mermaid code is not empty\n                if (!mermaidCode.trim()) {\n                    throw new Error('Empty mermaid code');\n                }\n                // Create a unique ID for this diagram\n                const diagramId = \"mermaid-\".concat(Date.now());\n                // Use mermaid.render to generate SVG\n                const { svg } = await mermaid__WEBPACK_IMPORTED_MODULE_7__[\"default\"].render(diagramId, mermaidCode);\n                // Insert the rendered SVG\n                flowchartRef.current.innerHTML = svg;\n                // Clear any previous errors\n                setError(null);\n            } catch (renderError) {\n                console.error('Error rendering flowchart:', renderError);\n                // Provide more specific error messages\n                let errorMessage = \"Error rendering flowchart.\";\n                if (renderError instanceof Error) {\n                    if (renderError.message.includes('Parse error') || renderError.message.includes('syntax')) {\n                        errorMessage = \"Invalid flowchart syntax. The generated code may contain errors.\";\n                    } else if (renderError.message.includes('Empty')) {\n                        errorMessage = \"No flowchart content was generated.\";\n                    } else {\n                        errorMessage = \"Rendering error: \".concat(renderError.message);\n                    }\n                }\n                setError(errorMessage);\n                // Show the raw mermaid code in case of error for debugging\n                if (flowchartRef.current) {\n                    flowchartRef.current.innerHTML = '\\n            <div class=\"p-4 bg-red-900/20 border border-red-500/30 rounded-lg\">\\n              <h4 class=\"text-red-400 font-medium mb-2\">Flowchart Rendering Error</h4>\\n              <p class=\"text-red-300 text-sm mb-3\">'.concat(errorMessage, '</p>\\n              <details class=\"text-xs\">\\n                <summary class=\"text-red-400 cursor-pointer\">Show raw code</summary>\\n                <pre class=\"mt-2 p-2 bg-black/30 rounded text-gray-300 overflow-auto\">').concat(mermaidCode, \"</pre>\\n              </details>\\n            </div>\\n          \");\n                }\n            }\n        }\n    };\n    const handleZoomIn = ()=>{\n        setZoom((prev)=>Math.min(prev + 0.2, 3));\n    };\n    const handleZoomOut = ()=>{\n        setZoom((prev)=>Math.max(prev - 0.2, 0.5));\n    };\n    const handleReset = ()=>{\n        setZoom(1);\n    };\n    const handleDownload = ()=>{\n        // In a real implementation, this would export the flowchart as SVG/PNG\n        console.log(\"Download flowchart\");\n    };\n    const toggleFullscreen = async ()=>{\n        const newFullscreenState = !isFullscreen;\n        setIsFullscreen(newFullscreenState);\n        // If opening fullscreen and we have flowchart data, render it in the fullscreen container\n        if (newFullscreenState && flowchartData && fullscreenFlowchartRef.current) {\n            setTimeout(async ()=>{\n                try {\n                    const diagramId = \"mermaid-fullscreen-\".concat(Date.now());\n                    const { svg } = await mermaid__WEBPACK_IMPORTED_MODULE_7__[\"default\"].render(diagramId, flowchartData);\n                    if (fullscreenFlowchartRef.current) {\n                        fullscreenFlowchartRef.current.innerHTML = svg;\n                    }\n                } catch (error) {\n                    console.error('Error rendering fullscreen flowchart:', error);\n                }\n            }, 100) // Small delay to ensure the modal is rendered\n            ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-black\",\n        ref: flowchartContainerRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-medium text-center\",\n                        children: \"Flowchart\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-sm text-muted-foreground mt-1\",\n                        children: \"Machine Learning Process Visualization\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: [\n                                \"Zoom: \",\n                                Math.round(zoom * 100),\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleZoomOut,\n                                size: \"sm\",\n                                variant: \"outline\",\n                                className: \"h-8 w-8 p-0\",\n                                disabled: zoom <= 0.5,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleReset,\n                                size: \"sm\",\n                                variant: \"outline\",\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleZoomIn,\n                                size: \"sm\",\n                                variant: \"outline\",\n                                className: \"h-8 w-8 p-0\",\n                                disabled: zoom >= 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: toggleFullscreen,\n                                size: \"sm\",\n                                variant: \"outline\",\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleDownload,\n                                size: \"sm\",\n                                className: \"bg-purple-600 hover:bg-purple-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                className: \"flex-1\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: !documentId ? \"No document selected\" : \"Checking for existing flowchart or generating new one...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, this) : error || !documentId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-red-500 mb-2\",\n                                children: !documentId ? \"No Document Selected\" : \"Error Loading Flowchart\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: !documentId ? \"Please select a document to view its flowchart.\" : error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, this),\n                            documentId && error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadFlowchart,\n                                className: \"mt-3 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-md transition-colors\",\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 11\n                }, this) : !flowchartData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Maximize_MessageSquare_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-muted-foreground mb-2\",\n                                children: \"No Flowchart Available\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-sm\",\n                                children: !documentId ? \"Please select a document to view its flowchart.\" : \"Unable to load or generate flowchart for this document.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"bg-neutral-800 border-neutral-700 p-6 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                ref: flowchartRef,\n                                className: \"flex items-center justify-center min-h-[500px]\",\n                                style: {\n                                    transform: \"scale(\".concat(zoom, \")\"),\n                                    transformOrigin: \"center top\"\n                                },\n                                transition: {\n                                    duration: 0.3\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            isFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-6\",\n                onClick: toggleFullscreen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full max-w-6xl max-h-[90vh] bg-neutral-800 border border-neutral-700 rounded-lg overflow-auto p-6\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-medium\",\n                                children: \"Machine Learning Process Flowchart\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                ref: fullscreenFlowchartRef,\n                                className: \"flex items-center justify-center min-h-[500px]\",\n                                style: {\n                                    transform: \"scale(\".concat(zoom, \")\"),\n                                    transformOrigin: \"center top\"\n                                },\n                                transition: {\n                                    duration: 0.3\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n                lineNumber: 304,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\flowchart-interface.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_s(FlowchartInterface, \"hTWUmOT4jwlpqX5nxZuQKcFSl5o=\", false, function() {\n    return [\n        _components_theme_provider__WEBPACK_IMPORTED_MODULE_4__.useTheme\n    ];\n});\n_c = FlowchartInterface;\nvar _c;\n$RefreshReg$(_c, \"FlowchartInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/flowchart-interface.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   chatApi: () => (/* binding */ chatApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   documentApi: () => (/* binding */ documentApi),\n/* harmony export */   generateFlashcards: () => (/* binding */ generateFlashcards),\n/* harmony export */   generateFlowchart: () => (/* binding */ generateFlowchart),\n/* harmony export */   generateQuiz: () => (/* binding */ generateQuiz),\n/* harmony export */   performanceApi: () => (/* binding */ performanceApi),\n/* harmony export */   processBlueprint: () => (/* binding */ processBlueprint),\n/* harmony export */   quizApi: () => (/* binding */ quizApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// Base URL for API requests\nconst API_BASE_URL = \"http://localhost:8000/api\" || 0;\n// Create axios instance with default config\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Add request interceptor to include auth token\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem('token');\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n});\n// Auth API endpoints\nconst authApi = {\n    signIn: async (credentials)=>{\n        try {\n            const response = await api.post('/users/login/', credentials);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    register: async (userData)=>{\n        try {\n            const response = await api.post('/users/register/', userData);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    verifyOTP: async (data)=>{\n        try {\n            const response = await api.post('/users/verify-otp/', data);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    logout: async ()=>{\n        try {\n            const response = await api.post('/users/logout/');\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n};\n// Document API endpoints\nconst documentApi = {\n    uploadDocument: async (file)=>{\n        const formData = new FormData();\n        formData.append('file', file);\n        try {\n            const response = await api.post('/documents/upload/', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getDocuments: async ()=>{\n        try {\n            const response = await api.get('/documents/');\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getDocumentStatus: async (documentId)=>{\n        try {\n            const response = await api.get(\"/documents/\".concat(documentId, \"/\"));\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n};\n// Chat API endpoints\nconst chatApi = {\n    sendMessage: async function(message, documentId) {\n        let model = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'openai';\n        try {\n            const response = await api.post('/chat/message/', {\n                message,\n                document_id: documentId,\n                model\n            });\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getHistory: async ()=>{\n        try {\n            const response = await api.get('/chat/history/');\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n};\n// Performance API endpoints\nconst performanceApi = {\n    createPerformance: async (performanceData)=>{\n        try {\n            const response = await api.post('/users/performance/', performanceData);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getPerformances: async (documentId)=>{\n        try {\n            const url = documentId ? \"/users/performance/?document=\".concat(documentId) : '/users/performance/';\n            const response = await api.get(url);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getStudentPerformances: async (studentId)=>{\n        try {\n            const response = await api.get(\"/users/performance/?student=\".concat(studentId));\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getPerformanceStats: async (documentId)=>{\n        try {\n            const response = await api.get(\"/users/performance/?document=\".concat(documentId));\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n};\n// Blueprint API (process blueprint)\nconst processBlueprint = async function(documentId, blueprintText) {\n    let llmModel = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'openai' // can be 'gemini', 'rag', 'openai', etc.\n    ;\n    try {\n        const response = await api.post(\"/process-blueprint/\".concat(documentId, \"/\"), {\n            document_id: documentId,\n            blueprint_text: blueprintText,\n            llm_model: llmModel\n        });\n        return response.data;\n    } catch (error) {\n        console.error('Error processing blueprint:', error);\n        throw error;\n    }\n};\n// Flashcards API\nconst generateFlashcards = async function(documentId) {\n    let numFlashcards = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, llmModel = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'openai';\n    try {\n        const response = await api.post(\"/flashcards/\".concat(documentId, \"/\"), {\n            num_flashcards: numFlashcards,\n            llm_model: llmModel\n        });\n        return response.data;\n    } catch (error) {\n        console.error('Error generating flashcards:', error);\n        throw error;\n    }\n};\n// Flowchart API\nconst generateFlowchart = async function(documentId) {\n    let llmModel = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'openai';\n    try {\n        const response = await api.post(\"/flowchart/\".concat(documentId, \"/\"), {\n            llm_model: llmModel\n        });\n        return response.data;\n    } catch (error) {\n        console.error('Error generating flowchart:', error);\n        throw error;\n    }\n};\n// Quiz API\nconst quizApi = {\n    generateQuiz: async function(documentId) {\n        let numQuestions = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5, llmModel = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'openai';\n        try {\n            const response = await api.post(\"/quizzes/\".concat(documentId, \"/\"), {\n                num_questions: numQuestions,\n                llm_model: llmModel\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error generating quiz:', error);\n            throw error;\n        }\n    },\n    getQuizzes: async (documentId)=>{\n        try {\n            const response = await api.get(\"/quizzes/?document=\".concat(documentId));\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching quizzes:', error);\n            throw error;\n        }\n    },\n    submitQuizAnswer: async (quizId, answers)=>{\n        try {\n            const response = await api.post(\"/quizzes/\".concat(quizId, \"/submit/\"), {\n                answers\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error submitting quiz:', error);\n            throw error;\n        }\n    }\n};\n// Legacy export for backward compatibility\nconst generateQuiz = quizApi.generateQuiz;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});