"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./components/chat-interface.tsx":
/*!***************************************!*\
  !*** ./components/chat-interface.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ImageIcon,MessageSquare,Paperclip,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Learning suggestions\nconst learningSuggestions = [\n    \"Explain the main concepts in this document\",\n    \"Create a summary of the key points\",\n    \"Generate practice questions about this content\",\n    \"How does this relate to [topic]?\",\n    \"What are the practical applications of this?\"\n];\nfunction ChatInterface() {\n    let { state, setState } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(state || []);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [suggestionsOpen, setSuggestionsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Update external state when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (setState) {\n                setState(messages);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        setState\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = ()=>{\n        if (!input.trim()) return;\n        // Add user message\n        const userMessage = {\n            id: Date.now().toString(),\n            content: input,\n            role: \"user\",\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput(\"\");\n        setIsLoading(true);\n        // Simulate AI response\n        setTimeout(()=>{\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"This is a placeholder response. In a real implementation, this would be connected to your AI API.\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            setIsLoading(false);\n        }, 1000);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleAttachmentClick = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    const handleImageClick = ()=>{\n        var _imageInputRef_current;\n        (_imageInputRef_current = imageInputRef.current) === null || _imageInputRef_current === void 0 ? void 0 : _imageInputRef_current.click();\n    };\n    const handleFileChange = (e)=>{\n        if (e.target.files && e.target.files.length > 0) {\n            var _documentId;\n            const file = e.target.files[0];\n            // For now, just show a message about file upload\n            // In a full implementation, you would upload the file first\n            const userMessage = {\n                id: Date.now().toString(),\n                content: \"I'd like to upload and discuss this file: \".concat(file.name),\n                role: \"user\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // Reset the file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n            // Send the message about the file to the AI\n            setIsLoading(true);\n            chatApi.sendMessage(\"I'd like to upload and discuss this file: \".concat(file.name), (_documentId = documentId) === null || _documentId === void 0 ? void 0 : _documentId.toString(), selectedModel).then((response)=>{\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    content: response.message || response.response || \"I understand you'd like to discuss a file. Please describe what you'd like to know about it.\",\n                    role: \"assistant\",\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]);\n            }).catch((error)=>{\n                console.error(\"Error sending file message:\", error);\n                const errorMessage = {\n                    id: (Date.now() + 1).toString(),\n                    content: \"I received your file reference, but I'm having trouble responding right now. Please try asking a question about it.\",\n                    role: \"assistant\",\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }).finally(()=>{\n                setIsLoading(false);\n            });\n        }\n    };\n    const handleSuggestionClick = (suggestion)=>{\n        setInput(suggestion);\n        setSuggestionsOpen(false);\n    };\n    const isEmpty = messages.length === 0 && !isLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-black\",\n        ref: chatContainerRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-medium\",\n                                children: \"Chat with the AI Tutor\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Select, {\n                                value: selectedModel,\n                                onValueChange: setSelectedModel,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectTrigger, {\n                                        className: \"w-32 bg-neutral-800 border-neutral-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectValue, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectItem, {\n                                                value: \"openai\",\n                                                children: \"OpenAI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectItem, {\n                                                value: \"gemini\",\n                                                children: \"Gemini\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    isEmpty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-sm text-muted-foreground\",\n                        children: \"Ask anything or use the suggestions below\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    documentId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-xs text-purple-400\",\n                        children: [\n                            \"Connected to document #\",\n                            documentId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: isEmpty ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex flex-col items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 rounded-full bg-neutral-800 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-12 w-12 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 p-4\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 max-w-[80%] \".concat(message.role === \"user\" ? \"flex-row-reverse\" : \"flex-row\"),\n                                    children: [\n                                        message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                            className: \"bg-purple-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-5 w-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                                    alt: \"Cognimosity Logo\",\n                                                    fill: true,\n                                                    className: \"object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                            className: \"bg-neutral-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg p-3 \".concat(message.role === \"user\" ? \"bg-purple-600 text-white\" : \"bg-neutral-800 text-neutral-100\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                        className: \"bg-purple-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-5 w-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                                alt: \"Cognimosity Logo\",\n                                                fill: true,\n                                                className: \"object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-lg p-3 bg-neutral-800 text-neutral-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.3s]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.15s]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-neutral-400 animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-neutral-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__.Collapsible, {\n                            open: suggestionsOpen,\n                            onOpenChange: setSuggestionsOpen,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__.CollapsibleTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Learning Suggestions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 transition-transform \".concat(suggestionsOpen ? \"rotate-180\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_6__.CollapsibleContent, {\n                                    className: \"space-y-2 pt-2\",\n                                    children: learningSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-sm\",\n                                            onClick: ()=>handleSuggestionClick(suggestion),\n                                            children: suggestion\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"file\",\n                                    ref: fileInputRef,\n                                    className: \"hidden\",\n                                    onChange: handleFileChange,\n                                    accept: \".pdf,.doc,.docx,.txt\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"file\",\n                                    ref: imageInputRef,\n                                    className: \"hidden\",\n                                    onChange: handleFileChange,\n                                    accept: \"image/*\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleAttachmentClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleImageClick,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyDown: handleKeyDown,\n                                    placeholder: \"Type your message...\",\n                                    className: \"min-h-[60px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleSendMessage,\n                                    disabled: !input.trim() || isLoading,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ImageIcon_MessageSquare_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\chat-interface.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"oz141vkl/782AQ75VyiO0ICa+SE=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat-interface.tsx\n"));

/***/ })

});