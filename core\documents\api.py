from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
import logging
import os
import requests

from .models import Document, DocumentEmbedding, BlueprintTopics
from .serializers import DocumentSerializer, DocumentEmbeddingSerializer, BlueprintTopicsSerializer

logger = logging.getLogger(__name__)

GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def store_embeddings(request, document_id):
    """
    Store document embeddings received from FastAPI server
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the chunks from the request
        chunks = request.data.get('chunks', [])

        if not chunks:
            return Response(
                {"error": "No chunks provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # First delete any existing embeddings for this document
        DocumentEmbedding.objects.filter(document=document).delete()

        # Create embedding objects for each chunk
        for chunk in chunks:
            DocumentEmbedding.objects.create(
                document=document,
                text_chunk=chunk['text'],
                embedding=chunk['embedding'],
                chunk_number=chunk['chunk_number']
            )

        # Update document status to completed
        document.processing_status = 'completed'
        document.error_message = None  # Clear any previous error
        document.save()

        return Response({
            "message": "Embeddings stored successfully",
            "document_id": document_id,
            "num_chunks": len(chunks)
        })

    except Exception as e:
        logger.error(f"Error storing embeddings: {str(e)}")
        return Response(
            {"error": f"Error storing embeddings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def store_topics(request, document_id):
    """
    Store blueprint topics received from FastAPI server
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the topics from the request
        topics = request.data.get('topics', [])

        if not topics:
            return Response(
                {"error": "No topics provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # First delete any existing topics for this document
        BlueprintTopics.objects.filter(document=document).delete()

        # Create topic objects for each topic
        created_topics = []
        for topic in topics:
            blueprint_topic = BlueprintTopics.objects.create(
                document=document,
                title=topic['title'],
                weightage=topic['weightage']
            )

            # Find relevant embeddings for this topic
            embeddings = DocumentEmbedding.objects.filter(document=document)

            if embeddings.exists():
                # Use semantic search to find relevant embeddings
                from sentence_transformers import SentenceTransformer
                import numpy as np

                # Initialize the sentence transformer model
                model = SentenceTransformer('all-MiniLM-L6-v2')

                # Generate embedding for the topic
                topic_embedding = model.encode([topic['title']])[0]

                # Calculate similarity scores
                similarities = []
                for idx, embedding_obj in enumerate(embeddings):
                    embedding_vector = np.array(embedding_obj.embedding)
                    similarity = np.dot(topic_embedding, embedding_vector) / (
                        np.linalg.norm(topic_embedding) * np.linalg.norm(embedding_vector)
                    )
                    similarities.append((similarity, idx, embedding_obj))

                # Sort by similarity (highest first)
                similarities.sort(reverse=True)

                # Get top 5 or 30% of embeddings, whichever is greater
                num_to_select = max(5, int(len(embeddings) * 0.3))
                relevant_embeddings = [emb for _, _, emb in similarities[:num_to_select]]

                # Add embeddings to the topic
                blueprint_topic.content.add(*relevant_embeddings)

            created_topics.append(blueprint_topic)

        # Update document status to completed
        document.processing_status = 'completed'
        document.error_message = None  # Clear any previous error
        document.save()

        return Response({
            "message": "Topics stored successfully",
            "document_id": document_id,
            "num_topics": len(created_topics)
        })

    except Exception as e:
        logger.error(f"Error storing topics: {str(e)}")
        return Response(
            {"error": f"Error storing topics: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_document_status(request, document_id):
    """
    Update document status
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the status from the request
        status_value = request.data.get('status')
        error_message = request.data.get('error_message')

        if not status_value:
            return Response(
                {"error": "No status provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update document status
        document.processing_status = status_value
        if error_message:
            document.error_message = error_message
        document.save()

        return Response({
            "message": "Document status updated successfully",
            "document_id": document_id,
            "status": status_value
        })

    except Exception as e:
        logger.error(f"Error updating document status: {str(e)}")
        return Response(
            {"error": f"Error updating document status: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def find_relevant_embeddings(request, document_id):
    """
    Find embeddings relevant to a topic
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the topic from the request
        topic = request.data.get('topic')

        if not topic:
            return Response(
                {"error": "No topic provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get all embeddings for this document
        embeddings = DocumentEmbedding.objects.filter(document=document)

        if not embeddings.exists():
            return Response(
                {"error": "Document has no embeddings"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Use semantic search to find relevant embeddings
        from sentence_transformers import SentenceTransformer
        import numpy as np

        # Initialize the sentence transformer model
        model = SentenceTransformer('all-MiniLM-L6-v2')

        # Generate embedding for the topic
        topic_embedding = model.encode([topic])[0]

        # Calculate similarity scores
        similarities = []
        for idx, embedding_obj in enumerate(embeddings):
            embedding_vector = np.array(embedding_obj.embedding)
            similarity = np.dot(topic_embedding, embedding_vector) / (
                np.linalg.norm(topic_embedding) * np.linalg.norm(embedding_vector)
            )
            similarities.append((similarity, idx, embedding_obj))

        # Sort by similarity (highest first)
        similarities.sort(reverse=True)

        # Get top 5 or 30% of embeddings, whichever is greater
        num_to_select = max(5, int(len(embeddings) * 0.3))
        relevant_embeddings = [emb for _, _, emb in similarities[:num_to_select]]

        return Response({
            "embedding_ids": [emb.id for emb in relevant_embeddings]
        })

    except Exception as e:
        logger.error(f"Error finding relevant embeddings: {str(e)}")
        return Response(
            {"error": f"Error finding relevant embeddings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_document_embeddings(request, document_id):
    """
    Get all embeddings for a document
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get all embeddings for this document
        embeddings = DocumentEmbedding.objects.filter(document=document).order_by('chunk_number')

        if not embeddings.exists():
            return Response(
                {"error": "Document has no embeddings"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Serialize the embeddings
        serializer = DocumentEmbeddingSerializer(embeddings, many=True)

        return Response(serializer.data)

    except Exception as e:
        logger.error(f"Error retrieving document embeddings: {str(e)}")
        return Response(
            {"error": f"Error retrieving document embeddings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def get_or_generate_flowchart(request, document_id):
    document = get_object_or_404(Document, id=document_id, user=request.user)

    if request.method == "GET":
        # Get existing flowchart for the document
        from .models import Flowchart
        try:
            flowchart = Flowchart.objects.get(document=document)
            return Response({
                "id": flowchart.id,
                "mermaid_code": flowchart.mermaid_code,
                "document": flowchart.document.id
            })
        except Flowchart.DoesNotExist:
            return Response([], status=200)  # Return empty array if no flowchart exists

    elif request.method == "POST":
        # Generate new flowchart
        # RAG: join all text chunks
        chunks = DocumentEmbedding.objects.filter(document=document).order_by('chunk_number')
        document_text = "\n\n".join(chunk.text_chunk for chunk in chunks)
        prompt = f"""
        You are an expert technical writer. Generate a flowchart using Mermaid syntax based on the following document content:

        "{document_text}"

        Only return valid Mermaid flowchart code. Do not add any explanations or text outside the Mermaid code.
        """
        try:
            response = requests.post(
                "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
                params={"key": GEMINI_API_KEY},
                json={
                    "contents": [
                        {"parts": [{"text": prompt}]}
                    ]
                },
                timeout=60
            )
            response.raise_for_status()
            gemini_output = response.json()
            generated_text = gemini_output.get("candidates", [])[0].get("content", {}).get("parts", [])[0].get("text", "")

            # Store the generated flowchart in the database
            from .models import Flowchart
            flowchart, created = Flowchart.objects.get_or_create(
                document=document,
                defaults={'mermaid_code': generated_text}
            )
            if not created:
                # Update existing flowchart
                flowchart.mermaid_code = generated_text
                flowchart.save()

            return Response({"flowchart": generated_text})
        except Exception as e:
            print(f"Error generating flowchart: {e}")
            return Response({"error": "Failed to generate flowchart."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def get_or_generate_flashcards(request, document_id):
    document = get_object_or_404(Document, id=document_id, user=request.user)

    if request.method == "GET":
        # Get existing flashcards for the document
        from .models import Flashcard
        flashcards = Flashcard.objects.filter(document=document)
        flashcard_data = [
            {
                "id": card.id,
                "front": card.front,
                "back": card.back,
                "document": card.document.id
            }
            for card in flashcards
        ]
        return Response(flashcard_data)

    elif request.method == "POST":
        # Generate new flashcards
        num_flashcards = int(request.data.get("num_flashcards", 10))
        chunks = DocumentEmbedding.objects.filter(document=document).order_by('chunk_number')
        document_text = "\n\n".join(chunk.text_chunk for chunk in chunks)
        prompt = f"""
        You are an expert tutor. Based on the following content, create {num_flashcards} flashcards.\n\nFormat:\nQuestion: ...\nAnswer: ...\n\nContent:\n{document_text}\n\nReturn only the flashcards, no extra text.
        """
        try:
            response = requests.post(
                "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
                params={"key": GEMINI_API_KEY},
                json={
                    "contents": [
                        {"parts": [{"text": prompt}]}
                    ]
                },
                timeout=60
            )
            response.raise_for_status()
            gemini_output = response.json()
            generated_text = gemini_output.get("candidates", [])[0].get("content", {}).get("parts", [])[0].get("text", "")
            # Parse Q/A pairs
            flashcards = []
            for qa in generated_text.strip().split("\n\n"):
                q = next((line for line in qa.split("\n") if line.strip().lower().startswith("question:")), None)
                a = next((line for line in qa.split("\n") if line.strip().lower().startswith("answer:")), None)
                if q and a:
                    flashcards.append({
                        "question": q.replace("Question:", "").strip(),
                        "answer": a.replace("Answer:", "").strip()
                    })
            return Response({"flashcards": flashcards})
        except Exception as e:
            print(f"Error generating flashcards: {e}")
            return Response({"error": "Failed to generate flashcards."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(["POST"])
@permission_classes([IsAuthenticated])
def generate_quiz(request, document_id):
    # document_id comes from URL parameter now
    num_questions = int(request.data.get("num_questions", 5))
    document = get_object_or_404(Document, id=document_id, user=request.user)
    chunks = DocumentEmbedding.objects.filter(document=document).order_by('chunk_number')
    document_text = "\n\n".join(chunk.text_chunk for chunk in chunks)
    prompt = f"""
    You are an educational quiz generator. Based on the following content, create {num_questions} quiz questions and their answers.\n\nFormat:\nQuestion: ...\nAnswer: ...\n\nContent:\n{document_text}\n\nReturn only the questions and answers, no extra text.
    """
    try:
        response = requests.post(
            "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
            params={"key": GEMINI_API_KEY},
            json={
                "contents": [
                    {"parts": [{"text": prompt}]}
                ]
            },
            timeout=60
        )
        response.raise_for_status()
        gemini_output = response.json()
        generated_text = gemini_output.get("candidates", [])[0].get("content", {}).get("parts", [])[0].get("text", "")
        # Parse Q/A pairs
        questions = []
        for qa in generated_text.strip().split("\n\n"):
            q = next((line for line in qa.split("\n") if line.strip().lower().startswith("question:")), None)
            a = next((line for line in qa.split("\n") if line.strip().lower().startswith("answer:")), None)
            if q and a:
                questions.append({
                    "question": q.replace("Question:", "").strip(),
                    "answer": a.replace("Answer:", "").strip()
                })
        return Response({"questions": questions})
    except Exception as e:
        print(f"Error generating quiz: {e}")
        return Response({"error": "Failed to generate quiz."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)




@api_view(["POST"])
@permission_classes([IsAuthenticated])
def process_blueprint_direct(request, document_id):
    """
    Direct endpoint for processing blueprints.
    Compatible with frontend API calls to /process-blueprint/{documentId}/
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get blueprint text and LLM model from request
        blueprint_text = request.data.get('blueprint_text')
        llm_model = request.data.get('llm_model', 'openai')

        # If no blueprint text provided, use the document's blueprint
        if not blueprint_text:
            if not document.blueprint:
                return Response(
                    {"error": "No blueprint text provided and document has no blueprint"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            blueprint_text = document.blueprint
        else:
            # Update document blueprint if new text provided
            document.blueprint = blueprint_text
            document.save()

        # Validate the LLM model
        if llm_model not in ['openai', 'gemini']:
            return Response(
                {"error": "Invalid LLM model. Use 'openai' or 'gemini'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the auth token
        auth_token = None
        if hasattr(request, 'auth') and request.auth:
            if hasattr(request.auth, 'token'):
                auth_token = request.auth.token
            elif hasattr(request.auth, 'key'):
                auth_token = request.auth.key
            else:
                auth_token = str(request.auth)

        if not auth_token:
            auth_token = f"user_{request.user.id}"

        # Set document status to processing
        document.processing_status = 'processing'
        document.save()

        # Import the direct processing function
        from .views import direct_process_blueprint

        # Try to use Celery task first, fall back to direct processing
        try:
            from .tasks import process_blueprint_task
            process_blueprint_task.delay(document.id, auth_token, llm_model)
            processing_method = "background task"
        except Exception as e:
            logger.warning(f"Celery task failed, falling back to direct processing: {str(e)}")
            # Fall back to direct processing
            result = direct_process_blueprint(document, auth_token, llm_model)
            processing_method = "direct request"

            # If there was an error, return it
            if 'error' in result:
                return Response({
                    "message": f"Blueprint processing failed: {result['error']}",
                    "document_id": document.id,
                    "status": "failed",
                    "error": result['error'],
                    "llm_model": llm_model
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({
            'message': f'Blueprint processing started via {processing_method} using {llm_model}.',
            'document_id': document.id,
            'status': 'processing',
            'llm_model': llm_model
        }, status=status.HTTP_202_ACCEPTED)

    except Exception as e:
        logger.error(f"Error processing blueprint for document {document_id}: {str(e)}")
        return Response(
            {"error": f"Error processing blueprint: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
